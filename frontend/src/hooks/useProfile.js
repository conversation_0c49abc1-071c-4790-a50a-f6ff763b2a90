import { useState, useEffect } from 'react';
import * as profileService from '../Services/profileService';

export const useProfile = () => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch current profile
  const fetchProfile = async () => {
    setLoading(true);
    setError(null);
    try {
      const profileData = await profileService.getCurrentProfile();
      setProfile(profileData);
      return profileData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update profile
  const updateProfile = async (profileData) => {
    setLoading(true);
    setError(null);
    try {
      const updatedProfile = await profileService.updateProfile(profileData);
      setProfile(updatedProfile);
      return updatedProfile;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update username
  const updateUsername = async (newUsername, currentPassword) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.updateUsername(newUsername, currentPassword);
      // Refresh profile to get updated data
      await fetchProfile();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update personal info
  const updatePersonalInfo = async (personalInfo) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.updatePersonalInfo(personalInfo);
      // Refresh profile to get updated data
      await fetchProfile();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update email
  const updateEmail = async (newEmail, currentPassword) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.updateEmail(newEmail, currentPassword);
      // Refresh profile to get updated data
      await fetchProfile();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const changePassword = async (currentPassword, newPassword, confirmationPassword) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.changePassword(currentPassword, newPassword, confirmationPassword);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Verify password
  const verifyPassword = async (password) => {
    try {
      return await profileService.verifyPassword(password);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Deactivate account
  const deactivateAccount = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.deactivateAccount();
      setProfile(null);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => setError(null);

  return {
    profile,
    loading,
    error,
    fetchProfile,
    updateProfile,
    updateUsername,
    updatePersonalInfo,
    updateEmail,
    changePassword,
    verifyPassword,
    deactivateAccount,
    clearError,
  };
};

export const useVehicles = () => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch vehicles
  const fetchVehicles = async () => {
    setLoading(true);
    setError(null);
    try {
      const vehicleData = await profileService.getVehicleRegistrations();
      setVehicles(vehicleData);
      return vehicleData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add vehicle
  const addVehicle = async (vehicleRegistration) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.addVehicleRegistration(vehicleRegistration);
      setVehicles(result.vehicleRegistrations || []);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Remove vehicle
  const removeVehicle = async (vehicleRegistration) => {
    setLoading(true);
    setError(null);
    try {
      const result = await profileService.removeVehicleRegistration(vehicleRegistration);
      // Refresh the vehicles list to ensure it's in sync with the server
      await fetchVehicles();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Clear error
  const clearError = () => setError(null);

  return {
    vehicles,
    loading,
    error,
    fetchVehicles,
    addVehicle,
    removeVehicle,
    clearError,
  };
};

export const useValidation = () => {
  const [loading, setLoading] = useState(false);

  // Check username availability
  const checkUsername = async (username) => {
    setLoading(true);
    try {
      return await profileService.checkUsernameAvailability(username);
    } catch (err) {
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check email availability
  const checkEmail = async (email) => {
    setLoading(true);
    try {
      return await profileService.checkEmailAvailability(email);
    } catch (err) {
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    checkUsername,
    checkEmail,
  };
};
