/* Center Selection Styles */
.center-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.selection-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.selection-header h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.selection-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* Centers Grid */
.centers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.center-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e3e6f0;
}

.center-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.center-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-bottom: 1px solid #e3e6f0;
}

.center-info {
  padding: 20px;
}

.center-info h3 {
  margin: 0 0 10px 0;
  color: #4e73df;
  font-size: 18px;
}

.center-details {
  margin-bottom: 15px;
  color: #5a5c69;
  font-size: 14px;
  line-height: 1.5;
}

.center-details p {
  margin: 5px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.center-details svg {
  color: #858796;
  width: 16px;
  height: 16px;
}

/* Action Buttons */
.center-actions {
  display: flex;
  gap: 10px;
  padding: 0 20px 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background-color: #4e73df;
  color: white;
  flex: 1;
}

.btn-primary:hover {
  background-color: #2e59d9;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 1px solid #d1d3e2;
  color: #4e73df;
}

.btn-outline:hover {
  background-color: #f8f9fc;
  border-color: #bac8f3;
}

/* Search and Filter */
.search-filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 250px;
  padding: 10px 15px;
  border: 1px solid #d1d3e2;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4e73df;
  box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
}

.filter-select {
  min-width: 200px;
  padding: 10px 15px;
  border: 1px solid #d1d3e2;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #6e707e;
}

/* No Results Message */
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  background: white;
  border-radius: 12px;
  grid-column: 1 / -1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .centers-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filter-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .search-input,
  .filter-select {
    width: 100%;
  }
}

/* Loading State */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  grid-column: 1 / -1;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4e73df;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
