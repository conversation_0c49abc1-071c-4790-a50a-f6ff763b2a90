import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getTechnicalCenters, getAvailableDates, getAvailableTimeSlots } from '../../Services/technicalCheckupService';
import './CenterSelection.css';

const CenterSelection = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [centers, setCenters] = useState([]);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const [availableDates, setAvailableDates] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingDates, setLoadingDates] = useState(false);
  const [loadingTimeSlots, setLoadingTimeSlots] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCenters();
  }, []);

  const fetchCenters = async () => {
    try {
      setLoading(true);
      const centerData = await getTechnicalCenters();
      setCenters(centerData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCenterSelect = async (center) => {
    try {
      setSelectedCenter(center);
      setLoadingDates(true);
      setError(null);
      
      console.log('Fetching available dates for center:', center.id);
      const dates = await getAvailableDates(center.id);
      console.log('Received available dates:', dates);
      
      if (!Array.isArray(dates)) {
        console.error('Expected an array of dates but received:', dates);
        setAvailableDates([]);
        return;
      }
      
      // Transform the dates to match the expected format
      const formattedDates = dates.map(date => ({
        date: date.date || date,
        time: date.time || 'All day'
      }));
      
      setAvailableDates(formattedDates);
      console.log('Formatted available dates:', formattedDates);
    } catch (err) {
      console.error('Error in handleCenterSelect:', err);
      setError(err.message || 'Failed to load available dates');
      setAvailableDates([]);
    } finally {
      setLoadingDates(false);
    }
  };

  const handleDateSelect = async (slot) => {
    setSelectedDate(slot);
    setSelectedTimeSlot(null);
    
    try {
      setLoadingTimeSlots(true);
      setError(null);
      
      // The date is already in the correct format from the backend (LocalDate)
      const formattedDate = slot.date;
      console.log('Fetching time slots for date:', formattedDate, 'at center:', selectedCenter.id);
      
      const slots = await getAvailableTimeSlots(selectedCenter.id, formattedDate);
      console.log('Received time slots:', slots);
      
      // Transform the slots to include both time and availability status
      const formattedSlots = slots.map(slot => ({
        time: slot.timeSlot, // This should be in 'HH:mm' format
        available: slot.isAvailable
      }));
      
      console.log('Formatted time slots:', formattedSlots);
      setAvailableTimeSlots(formattedSlots);
    } catch (err) {
      console.error('Error fetching time slots:', err);
      setError('Failed to load available time slots: ' + (err.message || 'Unknown error'));
      setAvailableTimeSlots([]);
    } finally {
      setLoadingTimeSlots(false);
    }
  };

  const handleTimeSlotSelect = (timeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleConfirmBooking = () => {
    if (selectedCenter && selectedDate && selectedTimeSlot) {
      // Navigate to confirmation page or complete booking
      navigate('/technical-checkup/confirmation', {
        state: {
          checkupId: location.state?.checkupId,
          vehicle: location.state?.vehicle,
          center: selectedCenter,
          date: selectedDate.date,
          timeSlot: selectedTimeSlot,
          appointmentDate: `${selectedDate.date}T${selectedTimeSlot}`
        }
      });
    }
  };

  if (loading) {
    return (
      <div className="center-selection-container">
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading technical centers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="center-selection-container">
      <div className="header-section">
        <h1>Select Technical Center</h1>
        <p>Choose a center and available date for your technical checkup</p>
      </div>

      {error && (
        <div className="error-message">
          <span>⚠️ {error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="selection-content">
        {/* Centers List */}
        <div className="centers-section">
          <h2>Available Centers</h2>
          <div className="centers-grid">
            {centers.map((center) => (
              <div
                key={center.id}
                className={`center-card ${selectedCenter?.id === center.id ? 'selected' : ''}`}
                onClick={() => handleCenterSelect(center)}
              >
                <div className="center-icon">🏢</div>
                <div className="center-info">
                  <h3>{center.name}</h3>
                  <p className="center-address">{center.address}</p>
                  <p className="center-city">{center.city}</p>
                  <div className="center-details">
                    <span>📞 {center.phone}</span>
                    <span>🕒 {center.openingTime} - {center.closingTime}</span>
                  </div>
                </div>
                {selectedCenter?.id === center.id && (
                  <div className="selection-indicator">✓</div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Date Selection */}
        {selectedCenter && (
          <div className="dates-section">
            <h2>Available Dates</h2>
            {loadingDates ? (
              <div className="dates-loading">
                <div className="loading-spinner-small"></div>
                <p>Loading available dates...</p>
              </div>
            ) : availableDates.length > 0 ? (
              <div className="dates-grid">
                {availableDates.map((date, index) => (
                  <div
                    key={index}
                    className={`date-card ${selectedDate?.date === date.date ? 'selected' : ''}`}
                    onClick={() => handleDateSelect(date)}
                  >
                    <div className="date-day">{new Date(date.date).getDate()}</div>
                    <div className="date-month">
                      {new Date(date.date).toLocaleDateString('en', { month: 'short' })}
                    </div>
                    <div className="date-weekday">
                      {new Date(date.date).toLocaleDateString('en', { weekday: 'short' })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-dates">
                <p>No available dates for this center. Please select another center.</p>
              </div>
            )}
            
            {/* Time Slots Section */}
            {selectedDate && (
              <div className="time-slots-section">
                <h3>Available Time Slots</h3>
                {loadingTimeSlots ? (
                  <div className="time-slots-loading">
                    <div className="loading-spinner-small"></div>
                    <p>Loading available time slots...</p>
                  </div>
                ) : availableTimeSlots.length > 0 ? (
                  <div className="time-slots-grid">
                    {availableTimeSlots
                      .filter(slot => slot.available)
                      .map((slot, index) => (
                        <div
                          key={index}
                          className={`time-slot-card ${
                            selectedTimeSlot === slot.time ? 'selected' : ''
                          }`}
                          onClick={() => handleTimeSlotSelect(slot.time)}
                        >
                          {slot.time}
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="no-time-slots">
                    <p>No available time slots for this date. Please select another date.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="navigation-section">
        <button 
          className="btn btn-secondary"
          onClick={() => navigate('/technical-checkup/form')}
        >
          ← Back to Form
        </button>
        
        {selectedCenter && selectedDate && selectedTimeSlot && (
          <button 
            className="btn btn-primary"
            onClick={handleConfirmBooking}
          >
            Confirm Booking →
          </button>
        )}
      </div>

      {/* Placeholder Message */}
      <div className="placeholder-message">
        <div className="placeholder-icon">🚧</div>
        <h3>Center & Date Selection</h3>
        <p>This is a placeholder for the center and date selection functionality.</p>
        <p>The backend integration for technical centers and available slots will be implemented next.</p>
      </div>
    </div>
  );
};

export default CenterSelection;
