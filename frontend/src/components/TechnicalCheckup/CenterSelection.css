/* Center Selection Styles */
.center-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-section h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.header-section p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* Loading States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #4e73df;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #4e73df;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
}

.error-message button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.error-message button:hover {
  opacity: 1;
}

/* Selection Content */
.selection-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

/* Centers Section */
.centers-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #495057;
}

.centers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.center-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  position: relative;
}

.center-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.center-card.selected {
  border-color: #4e73df;
  background: #f8f9ff;
}

.center-icon {
  font-size: 32px;
  text-align: center;
  margin-bottom: 16px;
  color: #4e73df;
}

.center-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #4a4b65;
}

.center-address, .center-city {
  margin: 4px 0;
  color: #6c757d;
  font-size: 14px;
}

.center-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.center-details span {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
}

.selection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: #4e73df;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Dates Section */
.dates-section {
  margin-top: 30px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.dates-section h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Time Slots Section */
.time-slots-section {
  margin-top: 30px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.time-slots-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.time-slot-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  color: #444;
}

.time-slot-card:hover {
  background: #f8f9ff;
  border-color: #4e73df;
  color: #4e73df;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(78, 115, 223, 0.15);
}

.time-slot-card.selected {
  background: #4e73df;
  border-color: #4e73df;
  color: white;
}

.time-slots-loading,
.no-time-slots {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 16px;
  color: #6c757d;
  font-size: 14px;
}

.time-slots-loading {
  flex-direction: column;
  gap: 10px;
}

.dates-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #495057;
}

.dates-loading {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-size: 14px;
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.date-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.date-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.date-card.selected {
  border-color: #4e73df;
  background: #f8f9ff;
}

.date-day {
  font-size: 24px;
  font-weight: 600;
  color: #4a4b65;
  line-height: 1;
  margin-bottom: 4px;
}

.date-month {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 4px;
}

.date-time {
  font-size: 13px;
  color: #4e73df;
  font-weight: 500;
  background: #f0f3ff;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-top: 6px;
}

.no-dates {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

/* Navigation Section */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  margin-top: 30px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 10px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: #4e73df;
  color: white;
}

.btn-primary:hover {
  background: #3a5bd9;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(78, 115, 223, 0.2);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Placeholder Message */
.placeholder-message {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.placeholder-message h3 {
  margin: 0 0 10px 0;
  color: #4a4b65;
}

.placeholder-message p {
  margin: 0 0 10px 0;
  line-height: 1.6;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .centers-grid, .dates-grid {
    grid-template-columns: 1fr;
  }
  
  .header-section {
    padding: 20px 15px;
  }
  
  .header-section h1 {
    font-size: 24px;
  }
  
  .header-section p {
    font-size: 14px;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .btn {
    width: 100%;
  }
}
