import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { scheduleAppointment } from '../../Services/technicalCheckupService';
import './Confirmation.css';

const Confirmation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  // Get booking details from location state
  const { 
    checkupId, 
    vehicle, 
    center, 
    date, 
    timeSlot,
    appointmentDate
  } = location.state || {};

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Handle booking confirmation
  const handleConfirm = async () => {
    if (!checkupId || !center?.id || !appointmentDate) {
      setError('Missing required booking information');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      console.log('Submitting appointment:', {
        checkupId,
        centerId: center.id,
        appointmentDate: appointmentDate.split('T')[0], // Extract date part
        timeSlot
      });
      
      // Call scheduleAppointment with individual parameters
      const result = await scheduleAppointment(
        checkupId,
        center.id,
        appointmentDate.split('T')[0], // Extract date part
        timeSlot
      );
      
      console.log('Appointment scheduled successfully:', result);
      setSuccess(true);
      
      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
      
    } catch (err) {
      console.error('Error scheduling appointment:', err);
      setError(err.message || 'Failed to schedule appointment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Redirect if missing required data
  useEffect(() => {
    if (!location.state) {
      navigate('/technical-checkup');
    }
  }, [location.state, navigate]);

  if (!location.state) {
    return (
      <div className="confirmation-container">
        <div className="loading">Loading booking details...</div>
      </div>
    );
  }

  return (
    <div className="confirmation-container">
      <h1>Confirm Your Appointment</h1>
      
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError('')}>×</button>
        </div>
      )}
      
      {success ? (
        <div className="success-message">
          <div className="success-icon">✓</div>
          <h2>Appointment Scheduled Successfully!</h2>
          <p>Your technical checkup has been scheduled. You'll be redirected to your dashboard shortly.</p>
        </div>
      ) : (
        <>
          <div className="booking-details">
            <div className="detail-section">
              <h2>Vehicle Information</h2>
              <div className="detail-item">
                <span className="label">Registration:</span>
                <span className="value">{vehicle?.registration || 'N/A'}</span>
              </div>
              <div className="detail-item">
                <span className="label">Type:</span>
                <span className="value">{vehicle?.type || 'N/A'}</span>
              </div>
              <div className="detail-item">
                <span className="label">Model:</span>
                <span className="value">{vehicle?.model || 'N/A'}</span>
              </div>
            </div>
            
            <div className="detail-section">
              <h2>Appointment Details</h2>
              <div className="detail-item">
                <span className="label">Center:</span>
                <span className="value">{center?.name || 'N/A'}</span>
              </div>
              <div className="detail-item">
                <span className="label">Location:</span>
                <span className="value">
                  {center?.address ? `${center.address}, ${center.city}` : 'N/A'}
                </span>
              </div>
              <div className="detail-item">
                <span className="label">Date:</span>
                <span className="value">{formatDate(date)}</span>
              </div>
              <div className="detail-item">
                <span className="label">Time:</span>
                <span className="value">
                  {timeSlot ? new Date(`2000-01-01T${timeSlot}`).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  }) : 'N/A'}
                </span>
              </div>
            </div>
          </div>
          
          <div className="confirmation-actions">
            <button 
              className="btn btn-secondary"
              onClick={() => navigate(-1)}
              disabled={loading}
            >
              Back
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleConfirm}
              disabled={loading}
            >
              {loading ? 'Confirming...' : 'Confirm Appointment'}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default Confirmation;
