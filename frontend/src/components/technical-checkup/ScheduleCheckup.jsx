import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typo<PERSON>, 
  Box, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Grid, 
  Paper,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, parseISO } from 'date-fns';
import { 
  getTechnicalCenters, 
  getAvailableDates, 
  getAvailableTimeSlots, 
  createTechnicalCheckup, 
  scheduleAppointment 
} from '../../services/technicalCheckupService';

const ScheduleCheckup = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [centers, setCenters] = useState([]);
  const [selectedCenter, setSelectedCenter] = useState('');
  const [availableDates, setAvailableDates] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState('');
  const [vehicleRegistration, setVehicleRegistration] = useState('');
  const [vehicles, setVehicles] = useState([]);

  // Fetch technical centers on component mount
  useEffect(() => {
    const fetchCenters = async () => {
      try {
        setLoading(true);
        const data = await getTechnicalCenters();
        setCenters(data);
      } catch (err) {
        setError('Failed to load technical centers');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCenters();
  }, []);

  // Fetch available dates when a center is selected
  useEffect(() => {
    if (selectedCenter) {
      const fetchDates = async () => {
        try {
          setLoading(true);
          const dates = await getAvailableDates(selectedCenter);
          setAvailableDates(dates);
        } catch (err) {
          setError('Failed to load available dates');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };

      fetchDates();
    }
  }, [selectedCenter]);

  // Fetch available time slots when a date is selected
  useEffect(() => {
    if (selectedDate && selectedCenter) {
      const fetchSlots = async () => {
        try {
          setLoading(true);
          const formattedDate = format(selectedDate, 'yyyy-MM-dd');
          const slots = await getAvailableTimeSlots(selectedCenter, formattedDate);
          setAvailableSlots(slots);
        } catch (err) {
          setError('Failed to load available time slots');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };

      fetchSlots();
    }
  }, [selectedDate, selectedCenter]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedCenter || !selectedDate || !selectedSlot) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // First create the checkup
      const checkupData = {
        vehicleRegistration,
        // Add other necessary fields
      };
      
      const newCheckup = await createTechnicalCheckup(checkupData);
      
      // Then schedule the appointment
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');
      await scheduleAppointment(
        newCheckup.id,
        selectedCenter,
        formattedDate,
        selectedSlot
      );
      
      setSuccess('Appointment scheduled successfully!');
      // Reset form
      setSelectedCenter('');
      setSelectedDate(null);
      setSelectedSlot('');
      setVehicleRegistration('');
    } catch (err) {
      setError(err.message || 'Failed to schedule appointment');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError('');
    setSuccess('');
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Schedule Technical Checkup
        </Typography>
        
        <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel id="center-label">Technical Center</InputLabel>
                  <Select
                    labelId="center-label"
                    value={selectedCenter}
                    onChange={(e) => setSelectedCenter(e.target.value)}
                    label="Technical Center"
                  >
                    {centers.map((center) => (
                      <MenuItem key={center.id} value={center.id}>
                        {center.name} - {center.city}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Select Date"
                      value={selectedDate}
                      onChange={(newValue) => setSelectedDate(newValue)}
                      renderInput={(params) => <TextField fullWidth {...params} />}
                      disablePast
                      shouldDisableDate={(date) => {
                        const formattedDate = format(date, 'yyyy-MM-dd');
                        return !availableDates.includes(formattedDate);
                      }}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth required disabled={!selectedDate}>
                  <InputLabel id="time-slot-label">Time Slot</InputLabel>
                  <Select
                    labelId="time-slot-label"
                    value={selectedSlot}
                    onChange={(e) => setSelectedSlot(e.target.value)}
                    label="Time Slot"
                  >
                    {availableSlots
                      .filter(slot => slot.available)
                      .map((slot) => (
                        <MenuItem key={slot.time} value={slot.time}>
                          {slot.time}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextField
                    label="Vehicle Registration"
                    value={vehicleRegistration}
                    onChange={(e) => setVehicleRegistration(e.target.value)}
                    fullWidth
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={loading || !selectedCenter || !selectedDate || !selectedSlot}
                  fullWidth
                  size="large"
                >
                  {loading ? <CircularProgress size={24} /> : 'Schedule Appointment'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Paper>
      </Box>
      
      <Snackbar
        open={!!error || !!success}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={error ? 'error' : 'success'}
          sx={{ width: '100%' }}
        >
          {error || success}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ScheduleCheckup;
