package whitecape.tech.apigateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;

import org.springframework.context.annotation.Bean;

@SpringBootApplication

public class ApiGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiGatewayApplication.class, args);
    }

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // Authentication Service Routes
                .route("auth-service", r -> r
                        .path("/api/v1/auth/**")
                        .uri("http://localhost:8082"))

                // Driver Profile Service Routes
                .route("driver-profile-service", r -> r
                        .path("/api/v1/driverprofile/**")
                        .uri("http://localhost:8082"))

                // Internal Service API
                .route("internal-service", r -> r
                        .path("/api/v1/internal/**")
                        .uri("http://localhost:8082"))

                // Health check route
                .route("health-check", r -> r
                        .path("/actuator/health")
                        .uri("http://localhost:8082"))

                // VT (Vehicle Technical checkup) Service
                .route("vt-service", r -> r
                        .path("/api/v1/vt/**")
                        .uri("http://localhost:8081"))

                // Future microservices can be added here
                // Vehicle Service
                .route("vehicle-service", r -> r
                        .path("/api/v1/vehicles/**")
                        .uri("http://localhost:8081"))

                // Insurance Service
                .route("insurance-service", r -> r
                        .path("/api/v1/insurance/**")
                        .uri("http://localhost:8084"))

                // Payment Service
                .route("payment-service", r -> r
                        .path("/api/v1/payments/**")
                        .uri("http://localhost:8085"))

                .build();
    }
}
