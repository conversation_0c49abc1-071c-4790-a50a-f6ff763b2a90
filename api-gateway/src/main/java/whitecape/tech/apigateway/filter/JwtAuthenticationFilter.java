package whitecape.tech.apigateway.filter;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.util.List;

@Component
@Slf4j
public class JwtAuthenticationFilter extends AbstractGatewayFilterFactory<JwtAuthenticationFilter.Config> {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${service.internal.token:vt-service-internal-token}")
    private String expectedInternalToken;

    // Public endpoints that don't require JWT authentication
    private static final List<String> PUBLIC_ENDPOINTS = List.of(
            "/api/v1/auth/register",
            "/api/v1/auth/authenticate",
            "/api/v1/auth/verify-email",
            "/api/v1/auth/resend-verification",
            "/api/v1/auth/email-verification-status",
            "/api/v1/driverprofile/check-username",
            "/api/v1/driverprofile/check-email",
            "/api/v1/driverprofile/getDrivers", // For testing
            "/api/v1/driverprofile/test", // For testing
            "/api/v1/vt/health", // VT service health
            "/api/v1/vt/info", // VT service info
            "/api/v1/vt/cors-test", // VT CORS testing
            "/api/v1/vt/vehicles/test", // VT vehicles test endpoint
            "/api/v1/vt/vehicles/simple", // VT simple vehicles endpoint
            "/api/v1/vt/checkups/blockchain-status", // Blockchain status endpoint
            "/api/v1/vt/checkups/test-blockchain", // Blockchain test endpoint
            "/api/v1/gateway", // Gateway debug endpoints
            "/test", // Test endpoints
            "/actuator/health"
    );

    // Internal service endpoints that use service token authentication
    private static final List<String> INTERNAL_SERVICE_ENDPOINTS = List.of(
            "/api/v1/internal"
    );

    public JwtAuthenticationFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();

            log.debug("Processing request to path: {}", path);

            // Skip authentication for public endpoints
            if (isPublicEndpoint(path)) {
                log.debug("Public endpoint, skipping authentication: {}", path);
                return chain.filter(exchange);
            }

            // Handle internal service endpoints with service token authentication
            if (isInternalServiceEndpoint(path)) {
                log.debug("Internal service endpoint, checking service token: {}", path);
                return handleInternalServiceAuthentication(exchange, chain);
            }

            // Check for Authorization header for regular JWT authentication
            String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                log.warn("Missing or invalid Authorization header for path: {}", path);
                return onError(exchange, "Missing or invalid Authorization header", HttpStatus.UNAUTHORIZED);
            }

            String token = authHeader.substring(7);

            try {
                // Validate JWT token
                Claims claims = validateToken(token);

                // Add user information to request headers for downstream services
                ServerHttpRequest modifiedRequest = request.mutate()
                        .header("X-User-Email", claims.getSubject())
                        .header("X-User-Authorities", String.join(",", (List<String>) claims.get("authorities")))
                        .build();

                log.debug("JWT token validated successfully for user: {}", claims.getSubject());

                return chain.filter(exchange.mutate().request(modifiedRequest).build());

            } catch (Exception e) {
                log.error("JWT token validation failed: {}", e.getMessage());
                return onError(exchange, "Invalid JWT token", HttpStatus.UNAUTHORIZED);
            }
        };
    }

    private boolean isPublicEndpoint(String path) {
        return PUBLIC_ENDPOINTS.stream().anyMatch(path::startsWith);
    }

    private boolean isInternalServiceEndpoint(String path) {
        return INTERNAL_SERVICE_ENDPOINTS.stream().anyMatch(path::startsWith);
    }

    private Mono<Void> handleInternalServiceAuthentication(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // Check for required internal service headers
        String serviceToken = request.getHeaders().getFirst("X-Service-Token");
        String userEmail = request.getHeaders().getFirst("X-User-Email");
        String requestingService = request.getHeaders().getFirst("X-Requesting-Service");

        if (serviceToken == null || userEmail == null) {
            log.warn("Missing required headers for internal service call to: {}", path);
            return onError(exchange, "Missing required service authentication headers", HttpStatus.UNAUTHORIZED);
        }

        // Validate service token
        if (!expectedInternalToken.equals(serviceToken)) {
            log.warn("Invalid service token for internal service call to: {} Expected: {} Got: {}",
                    path, expectedInternalToken, serviceToken);
            return onError(exchange, "Invalid service token", HttpStatus.UNAUTHORIZED);
        }

        log.debug("Internal service authentication successful for service: {} user: {} path: {}",
                 requestingService, userEmail, path);

        // Allow the request to proceed
        return chain.filter(exchange);
    }

    private Claims validateToken(String token) {
        // Use the same encoding as Client-auth service (BASE64 decode)
        byte[] keyBytes = Decoders.BASE64.decode(jwtSecret);
        SecretKey key = Keys.hmacShaKeyFor(keyBytes);
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    private Mono<Void> onError(ServerWebExchange exchange, String error, HttpStatus httpStatus) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(httpStatus);
        response.getHeaders().add("Content-Type", "application/json");

        String body = String.format("{\"error\": \"%s\", \"status\": %d}", error, httpStatus.value());

        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }

    public static class Config {
        // Configuration properties can be added here if needed
    }
}
