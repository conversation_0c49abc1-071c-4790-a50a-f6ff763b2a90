// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract CheckupHistory {
    struct Checkup {
        uint256 id;
        string vehicleRegistration;
        string centerName;
        uint256 checkupDate;
        string status;
        string result;
        string certificateNumber;
        bool exists;
    }

    mapping(uint256 => Checkup) public checkups;
    mapping(string => uint256[]) public checkupsByVehicle;
    uint256 public checkupCount;
    address public owner;

    event CheckupRecorded(
        uint256 indexed id,
        string indexed vehicleRegistration,
        string centerName,
        uint256 checkupDate,
        string status
    );

    constructor() {
        owner = msg.sender;
        checkupCount = 0;
    }

    function recordCheckup(
        string memory _vehicleRegistration,
        string memory _centerName,
        uint256 _checkupDate,
        string memory _status,
        string memory _result,
        string memory _certificateNumber
    ) public onlyOwner returns (uint256) {
        checkupCount++;
        checkups[checkupCount] = Checkup(
            checkupCount,
            _vehicleRegistration,
            _centerName,
            _checkupDate,
            _status,
            _result,
            _certificateNumber,
            true
        );
        
        checkupsByVehicle[_vehicleRegistration].push(checkupCount);

        emit CheckupRecorded(
            checkupCount,
            _vehicleRegistration,
            _centerName,
            _checkupDate,
            _status
        );

        return checkupCount;
    }

    function getCheckup(uint256 _id) public view returns (
        uint256 id,
        string memory vehicleRegistration,
        string memory centerName,
        uint256 checkupDate,
        string memory status,
        string memory result,
        string memory certificateNumber
    ) {
        require(checkups[_id].exists, "Checkup does not exist");
        Checkup memory c = checkups[_id];
        return (
            c.id,
            c.vehicleRegistration,
            c.centerName,
            c.checkupDate,
            c.status,
            c.result,
            c.certificateNumber
        );
    }

    function getCheckupsByVehicle(string memory _vehicleRegistration) 
        public 
        view 
        returns (uint256[] memory) 
    {
        return checkupsByVehicle[_vehicleRegistration];
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
}
