package service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.http.HttpService;
import org.web3j.tuples.generated.Tuple7;
import org.web3j.tx.gas.StaticGasProvider;

import java.math.BigInteger;
import java.util.List;

// Import the actual generated contract class
import Contracts.CheckupHistory;

@Slf4j
@Service
public class BlockchainService {

    @Value("${blockchain.node.url:http://localhost:7545}")
    private String nodeUrl;

    @Value("${blockchain.contract.address:}")
    private String contractAddress;

    @Value("${blockchain.wallet.privateKey:}")
    private String privateKey;

    private CheckupHistory contract;
    private Web3j web3j;
    private Credentials credentials;

    @PostConstruct
    public void init() {
        try {
            log.info("Initializing BlockchainService with node URL: {}", nodeUrl);
            log.info("Contract address: {}", contractAddress);

            // Check if blockchain is enabled
            if (contractAddress == null || contractAddress.trim().isEmpty()) {
                log.warn("Contract address not configured. Running in mock mode.");
                return;
            }

            if (privateKey == null || privateKey.trim().isEmpty()) {
                log.warn("Private key not configured. Running in mock mode.");
                return;
            }

            // Configure HTTP service with timeouts
            HttpService httpService = new HttpService(nodeUrl);
            httpService.addHeader("Content-Type", "application/json");
            httpService.addHeader("Accept", "application/json");

            // Build Web3j with minimal configuration
            web3j = Web3j.build(httpService);

            // Test connection to node
            try {
                String clientVersion = web3j.web3ClientVersion()
                        .send()
                        .getWeb3ClientVersion();
                log.info("Successfully connected to Ethereum node. Client version: {}", clientVersion);

                // Test basic blockchain access
                BigInteger blockNumber = web3j.ethBlockNumber()
                        .send()
                        .getBlockNumber();
                log.info("Current block number: {}", blockNumber);

            } catch (Exception e) {
                String errorMsg = String.format("Failed to connect to Ethereum node at %s. " +
                                "Please ensure Ganache is running and accessible. Error: %s",
                        nodeUrl, e.getMessage());
                log.error(errorMsg, e);
                log.warn("Falling back to mock mode");
                return; // Don't throw exception, just return
            }

            // Load credentials
            try {
                credentials = Credentials.create(privateKey);
                String address = credentials.getAddress();
                log.info("Loaded credentials for address: {}", address);

                // Get account nonce
                EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                        address, DefaultBlockParameterName.LATEST).send();
                log.info("Account nonce: {}", ethGetTransactionCount.getTransactionCount());

                // Ensure the address has the 0x prefix
                String normalizedAddress = contractAddress.startsWith("0x") ? contractAddress : "0x" + contractAddress;

                log.info("Attempting to load contract at address: {}", normalizedAddress);
                log.info("Using node URL: {}", nodeUrl);
                log.info("Using account: {}", credentials.getAddress());

                // Load the contract with minimal configuration
                // Create a static gas provider with reasonable defaults
                StaticGasProvider gasProvider = new StaticGasProvider(
                        BigInteger.valueOf(20_000_000_000L), // 20 Gwei
                        BigInteger.valueOf(3_000_000L)      // 3,000,000 gas
                );

                log.info("Loading contract with gas price: {}, gas limit: {}",
                        gasProvider.getGasPrice(), gasProvider.getGasLimit());

                // TODO: Replace with actual contract loading when contract class is generated
                // contract = CheckupHistory.load(normalizedAddress, web3j, credentials, gasProvider);
                log.info("Contract loading placeholder - implement when contract class is available");

            } catch (Exception e) {
                String errorMsg = String.format("Failed to load contract at address %s. " +
                                "Please verify the contract is deployed and the address is correct. Error: %s",
                        contractAddress, e.getMessage());
                log.error(errorMsg, e);
                log.warn("Falling back to mock mode");
                return; // Don't throw exception
            }

        } catch (Exception e) {
            log.error("Failed to initialize BlockchainService", e);
            log.warn("Running in mock mode due to initialization failure");
            // Don't throw exception, just log and continue
        }
    }

    public String recordCheckup(
            String vehicleRegistration,
            String centerName,
            long checkupDate,
            String status,
            String result,
            String certificateNumber
    ) throws Exception {
        if (contract == null) {
            log.warn("Blockchain not available, using mock transaction hash");
            return "0x" + java.util.UUID.randomUUID().toString().replace("-", "");
        }

        // TODO: Implement when contract class is available
        // return contract.recordCheckup(vehicleRegistration, centerName,
        //     BigInteger.valueOf(checkupDate), status, result, certificateNumber)
        //     .send().getTransactionHash();

        log.info("Mock blockchain record for vehicle: {}", vehicleRegistration);
        return "0x" + java.util.UUID.randomUUID().toString().replace("-", "");
    }

    public Object getCheckup(BigInteger checkupId) throws Exception {
        if (contract == null) {
            log.warn("Blockchain not available, returning mock data");
            return null;
        }

        // TODO: Implement when contract class is available
        // return contract.getCheckup(checkupId).send();

        log.info("Mock blockchain get checkup for ID: {}", checkupId);
        return null;
    }

    public List<BigInteger> getCheckupsByVehicle(String vehicleRegistration) throws Exception {
        if (contract == null) {
            log.warn("Blockchain not available, returning empty list");
            return List.of();
        }

        // TODO: Implement when contract class is available
        // return contract.getCheckupsByVehicle(vehicleRegistration).send();

        log.info("Mock blockchain get checkups for vehicle: {}", vehicleRegistration);
        return List.of();
    }
}
