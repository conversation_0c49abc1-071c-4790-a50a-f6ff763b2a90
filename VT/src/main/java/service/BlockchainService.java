package service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.http.HttpService;
import org.web3j.tuples.generated.Tuple7;
import org.web3j.tx.gas.StaticGasProvider;

import java.math.BigInteger;
import java.util.List;


    @Slf4j
    @Service
    public class BlockchainService {

        @Value("${blockchain.node.url:http://localhost:7545}")
        private String nodeUrl;

        @Value("${blockchain.contract.address:}")
        private String contractAddress;

        @Value("${blockchain.wallet.privateKey:}")
        private String privateKey;

        private CheckupHistory contract;
        private Web3j web3j;

        @PostConstruct
        public void init() {
            try {
                log.info("Initializing BlockchainService with node URL: {}", nodeUrl);
                log.info("Contract address: {}", contractAddress);

                if (contractAddress == null || contractAddress.trim().isEmpty()) {
                    throw new IllegalStateException("Contract address is not configured");
                }

                if (privateKey == null || privateKey.trim().isEmpty()) {
                    throw new IllegalStateException("Private key is not configured");
                }

                // Configure HTTP service with timeouts
                HttpService httpService = new HttpService(nodeUrl);
                httpService.addHeader("Content-Type", "application/json");
                httpService.addHeader("Accept", "application/json");

                // Build Web3j with minimal configuration
                web3j = Web3j.build(httpService);

                // Test connection to node
                try {
                    String clientVersion = web3j.web3ClientVersion()
                            .send()
                            .getWeb3ClientVersion();
                    log.info("Successfully connected to Ethereum node. Client version: {}", clientVersion);

                    // Test basic blockchain access
                    BigInteger blockNumber = web3j.ethBlockNumber()
                            .send()
                            .getBlockNumber();
                    log.info("Current block number: {}", blockNumber);

                } catch (Exception e) {
                    String errorMsg = String.format("Failed to connect to Ethereum node at %s. " +
                                    "Please ensure Ganache is running and accessible. Error: %s",
                            nodeUrl, e.getMessage());
                    log.error(errorMsg, e);
                    throw new RuntimeException(errorMsg, e);
                }

                // Load credentials
                try {
                    Credentials credentials = Credentials.create(privateKey);
                    String address = credentials.getAddress();
                    log.info("Loaded credentials for address: {}", address);

                    // Get account nonce
                    EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                            address, DefaultBlockParameterName.LATEST).send();
                    log.info("Account nonce: {}", ethGetTransactionCount.getTransactionCount());

                    // Validate and log contract address
                    if (contractAddress == null || contractAddress.trim().isEmpty()) {
                        throw new IllegalStateException("Contract address is not configured");
                    }

                    // Ensure the address has the 0x prefix
                    String normalizedAddress = contractAddress.startsWith("0x") ? contractAddress : "0x" + contractAddress;

                    log.info("Attempting to load contract at address: {}", normalizedAddress);
                    log.info("Using node URL: {}", nodeUrl);
                    log.info("Using account: {}", credentials.getAddress());

                    // Load the contract with minimal configuration
                    // Create a static gas provider with reasonable defaults
                    StaticGasProvider gasProvider = new StaticGasProvider(
                            BigInteger.valueOf(20_000_000_000L), // 20 Gwei
                            BigInteger.valueOf(3_000_000L)      // 3,000,000 gas
                    );

                    log.info("Loading contract with gas price: {}, gas limit: {}",
                            gasProvider.getGasPrice(), gasProvider.getGasLimit());

                    // Load the contract
                    contract = CheckupHistory.load(
                            normalizedAddress,
                            web3j,
                            credentials,
                            gasProvider
                    );
                }
                );

                // Test contract connection
                String loadedAddress = contract.getContractAddress();
                log.info("Successfully loaded contract. Address: {}", loadedAddress);

            } catch (Exception e) {
                String errorMsg = String.format("Failed to load contract at address %s. " +
                                "Please verify the contract is deployed and the address is correct. Error: %s",
                        contractAddress, e.getMessage());
                log.error(errorMsg, e);
                throw new RuntimeException(errorMsg, e);
            }

        } catch (Exception e) {
            log.error("Failed to initialize BlockchainService", e);
            throw new RuntimeException("Failed to initialize blockchain service: " + e.getMessage(), e);
        }
    }

    public String recordCheckup(
            String vehicleRegistration,
            String centerName,
            long checkupDate,
            String status,
            String result,
            String certificateNumber
    ) throws Exception {
        return contract.recordCheckup(
                vehicleRegistration,
                centerName,
                BigInteger.valueOf(checkupDate),
                status,
                result,
                certificateNumber
        ).send().getTransactionHash();
    }

    public Tuple7<BigInteger, String, String, BigInteger, String, String, String> getCheckup(BigInteger checkupId) throws Exception {
        return contract.getCheckup(checkupId).send();
    }

    public List<BigInteger> getCheckupsByVehicle(String vehicleRegistration) throws Exception {
        return contract.getCheckupsByVehicle(vehicleRegistration).send();
    }
}

}
