package service;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.http.HttpService;
import org.web3j.tuples.generated.Tuple7;
import org.web3j.tx.gas.StaticGasProvider;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

// Import the actual generated contract class
import Contracts.CheckupHistory;

@Slf4j
@Service
public class BlockchainService {

    @Value("${blockchain.node.url:http://localhost:7545}")
    private String nodeUrl;

    @Value("${blockchain.contract.address:}")
    private String contractAddress;

    @Value("${blockchain.wallet.privateKey:}")
    private String privateKey;

    private CheckupHistory contract;
    private Web3j web3j;
    private Credentials credentials;

    @PostConstruct
    public void init() {
        try {
            log.info("🔗 Initializing BlockchainService...");
            log.info("🔗 Node URL: {}", nodeUrl);
            log.info("🔗 Contract address: '{}' (length: {})", contractAddress, contractAddress != null ? contractAddress.length() : 0);
            log.info("🔗 Private key configured: {}", privateKey != null && !privateKey.trim().isEmpty());

            // Debug the exact values
            System.out.println("DEBUG - Raw contract address: [" + contractAddress + "]");
            System.out.println("DEBUG - Raw private key: [" + (privateKey != null ? privateKey.substring(0, 10) + "..." : "null") + "]");

            // Check if blockchain is enabled
            if (contractAddress == null || contractAddress.trim().isEmpty()) {
                log.warn("⚠️ Contract address not configured. Running in mock mode.");
                return;
            }

            if (privateKey == null || privateKey.trim().isEmpty()) {
                log.warn("⚠️ Private key not configured. Running in mock mode.");
                return;
            }

            // Test basic HTTP connectivity first
            try {
                log.info("🔗 Testing HTTP connectivity to: {}", nodeUrl);
                java.net.URL url = new java.net.URL(nodeUrl);
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);

                // Send a simple JSON-RPC request
                String jsonRequest = "{\"jsonrpc\":\"2.0\",\"method\":\"web3_clientVersion\",\"params\":[],\"id\":1}";
                connection.getOutputStream().write(jsonRequest.getBytes());

                int responseCode = connection.getResponseCode();
                log.info("✅ HTTP connectivity test: Response code {}", responseCode);
                connection.disconnect();
            } catch (Exception e) {
                log.error("❌ HTTP connectivity test failed: {}", e.getMessage());
                log.warn("🔄 Falling back to mock mode");
                return;
            }

            // Configure HTTP service with timeouts
            HttpService httpService = new HttpService(nodeUrl);
            httpService.addHeader("Content-Type", "application/json");
            httpService.addHeader("Accept", "application/json");

            // Build Web3j with minimal configuration
            web3j = Web3j.build(httpService);

            // Test connection to node
            try {
                log.info("🔗 Testing connection to Ganache node...");

                // Test basic blockchain access first
                BigInteger blockNumber = web3j.ethBlockNumber()
                        .send()
                        .getBlockNumber();
                log.info("✅ Connected to blockchain. Current block number: {}", blockNumber);

                // Get client version if available
                try {
                    String clientVersion = web3j.web3ClientVersion()
                            .send()
                            .getWeb3ClientVersion();
                    log.info("✅ Node client version: {}", clientVersion);
                } catch (Exception clientEx) {
                    log.warn("⚠️ Could not get client version, but connection is working");
                }

            } catch (Exception e) {
                String errorMsg = String.format("❌ Failed to connect to Ganache node at %s. " +
                                "Please ensure Ganache is running on port 7545. Error: %s",
                        nodeUrl, e.getMessage());
                log.error(errorMsg);
                log.warn("🔄 Falling back to mock mode");
                return; // Don't throw exception, just return
            }

            // Load credentials
            try {
                credentials = Credentials.create(privateKey);
                String address = credentials.getAddress();
                log.info("Loaded credentials for address: {}", address);

                // Get account nonce
                EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                        address, DefaultBlockParameterName.LATEST).send();
                log.info("Account nonce: {}", ethGetTransactionCount.getTransactionCount());

                // Validate and normalize contract address
                String normalizedAddress = contractAddress.trim();
                if (!normalizedAddress.startsWith("0x")) {
                    normalizedAddress = "0x" + normalizedAddress;
                }

                // Validate address format
                if (normalizedAddress.length() != 42) {
                    throw new IllegalArgumentException("Invalid contract address format: " + contractAddress);
                }

                log.info("🔗 Loading contract at address: {}", normalizedAddress);
                log.info("🔗 Using node URL: {}", nodeUrl);
                log.info("🔗 Using account: {}", credentials.getAddress());

                // Load the contract with minimal configuration
                // Create a static gas provider with reasonable defaults
                StaticGasProvider gasProvider = new StaticGasProvider(
                        BigInteger.valueOf(20_000_000_000L), // 20 Gwei
                        BigInteger.valueOf(3_000_000L)      // 3,000,000 gas
                );

                log.info("Loading contract with gas price: {}, gas limit: {}",
                        gasProvider.getGasPrice(), gasProvider.getGasLimit());

                // Load the actual contract
                contract = CheckupHistory.load(normalizedAddress, web3j, credentials, gasProvider);
                log.info("✅ Contract loaded successfully at address: {}", contract.getContractAddress());

            } catch (Exception e) {
                String errorMsg = String.format("Failed to load contract at address %s. " +
                                "Please verify the contract is deployed and the address is correct. Error: %s",
                        contractAddress, e.getMessage());
                log.error(errorMsg, e);
                log.warn("Falling back to mock mode");
                return; // Don't throw exception
            }

        } catch (Exception e) {
            log.error("Failed to initialize BlockchainService", e);
            log.warn("Running in mock mode due to initialization failure");
            // Don't throw exception, just log and continue
        }
    }

    public String recordCheckup(
            String vehicleRegistration,
            String centerName,
            long checkupDate,
            String status,
            String result,
            String certificateNumber
    ) throws Exception {
        if (contract == null) {
            log.warn("⚠️ Blockchain not available, using mock transaction hash");
            return "0x" + java.util.UUID.randomUUID().toString().replace("-", "");
        }

        try {
            log.info("🔗 Recording checkup on blockchain for vehicle: {}", vehicleRegistration);
            log.info("🔗 Center: {}, Date: {}, Status: {}, Result: {}",
                    centerName, checkupDate, status, result);

            // Call the actual smart contract
            var transactionReceipt = contract.recordCheckup(
                vehicleRegistration,
                centerName,
                BigInteger.valueOf(checkupDate),
                status,
                result,
                certificateNumber
            ).send();

            String txHash = transactionReceipt.getTransactionHash();
            log.info("✅ Checkup recorded on blockchain. TX Hash: {}", txHash);
            return txHash;

        } catch (Exception e) {
            log.error("❌ Failed to record checkup on blockchain: {}", e.getMessage());
            log.warn("🔄 Falling back to mock transaction hash");
            return "0x" + java.util.UUID.randomUUID().toString().replace("-", "");
        }
    }

    public Object getCheckup(BigInteger checkupId) throws Exception {
        if (contract == null) {
            log.warn("⚠️ Blockchain not available, returning mock data");
            return null;
        }

        try {
            log.info("🔍 Getting checkup from blockchain for ID: {}", checkupId);
            var result = contract.getCheckup(checkupId).send();
            log.info("✅ Retrieved checkup from blockchain");
            return result;
        } catch (Exception e) {
            log.error("❌ Failed to get checkup from blockchain: {}", e.getMessage());
            return null;
        }
    }

    public List<BigInteger> getCheckupsByVehicle(String vehicleRegistration) throws Exception {
        if (contract == null) {
            log.warn("⚠️ Blockchain not available, returning empty list");
            return List.of();
        }

        try {
            log.info("🔍 Getting checkups from blockchain for vehicle: {}", vehicleRegistration);
            List<BigInteger> result = contract.getCheckupsByVehicle(vehicleRegistration).send();
            log.info("✅ Retrieved {} checkups from blockchain for vehicle: {}", result.size(), vehicleRegistration);
            return result;
        } catch (Exception e) {
            log.error("❌ Failed to get checkups from blockchain: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * Check if blockchain is available
     */
    public boolean isBlockchainAvailable() {
        return contract != null && web3j != null;
    }

    /**
     * Get blockchain status
     */
    public Map<String, Object> getBlockchainStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("nodeUrl", nodeUrl);
        status.put("contractAddress", contractAddress);
        status.put("isAvailable", isBlockchainAvailable());

        if (web3j != null) {
            try {
                BigInteger blockNumber = web3j.ethBlockNumber().send().getBlockNumber();
                status.put("currentBlock", blockNumber.toString());
                status.put("nodeConnected", true);
            } catch (Exception e) {
                status.put("nodeConnected", false);
                status.put("nodeError", e.getMessage());
            }
        } else {
            status.put("nodeConnected", false);
        }

        return status;
    }
}
