package service;

import dto.CreateCheckupRequest;
import dto.TechnicalCheckupDTO;
import model.TechnicalCheckup;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface TechnicalCheckupService {
    
    /**
     * Create a new technical checkup request
     */
    TechnicalCheckupDTO createCheckup(CreateCheckupRequest request, Integer driverId);
    
    /**
     * Get all checkups for a driver
     */
    List<TechnicalCheckupDTO> getDriverCheckups(Integer driverId);
    
    /**
     * Get checkup by ID
     */
    TechnicalCheckupDTO getCheckupById(Integer checkupId);
    
    /**
     * Update checkup status
     */
    TechnicalCheckupDTO updateCheckupStatus(Integer checkupId, TechnicalCheckup.CheckupStatus status);
    
    /**
     * Schedule checkup appointment
     */
    TechnicalCheckupDTO scheduleAppointment(Integer checkupId, Integer centerId, String appointmentDate, String appointmentTime);
    
    /**
     * Cancel checkup
     */
    void cancelCheckup(Integer checkupId, Integer driverId);
    
    /**
     * Get checkup history for a driver
     */
    List<TechnicalCheckupDTO> getDriverCheckupHistory(Integer driverId);
    
    /**
     * Complete checkup with result
     */
    TechnicalCheckupDTO completeCheckup(Integer checkupId, String result, String certificateNumber);

    /**
     * Test blockchain recording
     */
    String testBlockchainRecording() throws Exception;
}
