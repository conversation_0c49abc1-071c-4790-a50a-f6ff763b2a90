package service;

import service.BlockchainService;
import dto.AvailableSlotDTO;
import dto.CreateCheckupRequest;
import dto.TechnicalCheckupDTO;
import model.TechnicalCheckup;
import model.AvailableSlot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import repository.AvailableSlotRepository;
import repository.TechnicalCheckupRepository;
import repository.TechnicalCenterRepository;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class TechnicalCheckupServiceImpl implements TechnicalCheckupService {

    @Autowired
    private TechnicalCheckupRepository checkupRepository;
    
    @Autowired
    private TechnicalCenterRepository centerRepository;
    
    @Autowired
    private AvailableSlotRepository slotRepository;
    
    @Autowired
    private TechnicalCenterService centerService;
    
@Autowired
    private  BlockchainService blockchainService;



    @Override
    public TechnicalCheckupDTO createCheckup(CreateCheckupRequest request, Integer driverId) {
        TechnicalCheckup checkup = new TechnicalCheckup();
        checkup.setDriverId(driverId);
        checkup.setVehicleRegistration(request.getVehicleRegistration());
        checkup.setVehicleType(request.getVehicleType());
        checkup.setVehicleModel(request.getVehicleModel());
        checkup.setVehicleYear(request.getVehicleYear());
        checkup.setChassisNumber(request.getChassisNumber());
        checkup.setVehicleColor(request.getVehicleColor());
        checkup.setVehicleFuelType(request.getVehicleFuelType());
        checkup.setNotes(request.getNotes());
        checkup.setStatus(TechnicalCheckup.CheckupStatus.PENDING);
        checkup.setCreatedDate(LocalDateTime.now());
        checkup.setUpdatedDate(LocalDateTime.now());

        TechnicalCheckup savedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(savedCheckup);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCheckupDTO> getDriverCheckups(Integer driverId) {
        List<TechnicalCheckup> checkups = checkupRepository.findByDriverId(driverId);
        return checkups.stream()
                .map(TechnicalCheckupDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public TechnicalCheckupDTO getCheckupById(Integer checkupId) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        return new TechnicalCheckupDTO(checkup);
    }

    @Override
    public TechnicalCheckupDTO updateCheckupStatus(Integer checkupId, TechnicalCheckup.CheckupStatus status) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        // If status is changing to COMPLETED, record on blockchain
        if (status == TechnicalCheckup.CheckupStatus.COMPLETED && 
            checkup.getStatus() != TechnicalCheckup.CheckupStatus.COMPLETED) {
            try {
                String txHash = blockchainService.recordCheckup(
                    checkup.getVehicleRegistration(),
                    checkup.getCenterName(),
                    checkup.getAppointmentDate().atStartOfDay(ZoneOffset.UTC).toEpochSecond(),
                    status.name(),
                    checkup.getResult() != null ? checkup.getResult() : "",
                    checkup.getCertificateNumber() != null ? checkup.getCertificateNumber() : ""
                );
                log.info("Completed checkup recorded on blockchain. TX Hash: {}", txHash);
                // Uncomment the following lines if you want to store the transaction hash
                // checkup.setBlockchainTxHash(txHash);
            } catch (Exception e) {
                log.error("Failed to record completed checkup on blockchain", e);
                // Handle error appropriately
            }
        }
        
        checkup.setStatus(status);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }

    @Override
    public TechnicalCheckupDTO scheduleAppointment(Integer checkupId, Integer centerId, String appointmentDate, String appointmentTime) {
        // Find the checkup
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        // Check if checkup is in a state that can be scheduled
        if (!checkup.getStatus().equals(TechnicalCheckup.CheckupStatus.PENDING)) {
            throw new IllegalStateException("Checkup cannot be scheduled in its current state: " + checkup.getStatus());
        }
        
        // Check if vehicle already has an active checkup (SCHEDULED or IN_PROGRESS)
        List<TechnicalCheckup> activeCheckups = checkupRepository.findByVehicleRegistration(checkup.getVehicleRegistration())
                .stream()
                .filter(c -> (c.getStatus() == TechnicalCheckup.CheckupStatus.SCHEDULED || 
                            c.getStatus() == TechnicalCheckup.CheckupStatus.IN_PROGRESS) &&
                           !c.getId().equals(checkupId))
                .collect(Collectors.toList());
                
        if (!activeCheckups.isEmpty()) {
            throw new IllegalStateException("This vehicle already has an active or pending checkup. " +
                    "Please cancel the existing checkup before scheduling a new one.");
        }
        
        // Parse the appointment date and time
        LocalDate date = LocalDate.parse(appointmentDate);
        LocalTime time = LocalTime.parse(appointmentTime);
        
        // Check if the slot is available
        List<AvailableSlotDTO> availableSlots = centerService.getAvailableSlots(centerId, date);
        boolean slotAvailable = availableSlots.stream()
                .anyMatch(slot -> slot.getTimeSlot().equals(time) && slot.getIsAvailable());
                
        if (!slotAvailable) {
            throw new IllegalStateException("The selected time slot is no longer available");
        }
        
        // Get center information
        var center = centerRepository.findById(centerId)
                .orElseThrow(() -> new RuntimeException("Technical center not found with id: " + centerId));
        
        // Update checkup with appointment details
        checkup.setCenterId(centerId);
        checkup.setCenterName(center.getName());
        checkup.setAppointmentDate(date);
        checkup.setAppointmentTime(appointmentTime);
        checkup.setStatus(TechnicalCheckup.CheckupStatus.SCHEDULED);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        // Book the slot
        bookAvailableSlot(centerId, date, time, checkup.getDriverId(), checkupId);
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }
    
    /**
     * Book an available slot for a checkup
     */
    private void bookAvailableSlot(Integer centerId, LocalDate date, LocalTime time, Integer driverId, Integer checkupId) {
        // Find the first available slot that matches the criteria
        Optional<AvailableSlot> slotOpt = slotRepository.findByCenterIdAndDateAndTimeSlot(centerId, date, time);
        
        if (slotOpt.isEmpty()) {
            throw new IllegalStateException("No available slot found for the selected date and time");
        }
        
        AvailableSlot slot = slotOpt.get();
        if (!slot.getIsAvailable()) {
            throw new IllegalStateException("The selected slot is no longer available");
        }
        
        // Book the slot
        slot.setIsAvailable(false);
        slot.setBookedByDriverId(driverId);
        slot.setCheckupId(checkupId);
        slotRepository.save(slot);
    }

    @Override
    public void cancelCheckup(Integer checkupId, Integer driverId) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        // Verify the checkup belongs to the driver
        if (!checkup.getDriverId().equals(driverId)) {
            throw new RuntimeException("You can only cancel your own checkups");
        }
        
        // If the checkup is scheduled, free up the slot
        if (checkup.getStatus() == TechnicalCheckup.CheckupStatus.SCHEDULED && 
            checkup.getCenterId() != null && 
            checkup.getAppointmentDate() != null && 
            checkup.getAppointmentTime() != null) {
            
            freeUpSlot(checkup.getCenterId(), checkup.getAppointmentDate(), LocalTime.parse(checkup.getAppointmentTime()));
        }
        
        checkup.setStatus(TechnicalCheckup.CheckupStatus.CANCELLED);
        checkup.setUpdatedDate(LocalDateTime.now());
        
        checkupRepository.save(checkup);
    }
    
    /**
     * Free up a slot when a checkup is cancelled
     */
    private void freeUpSlot(Integer centerId, LocalDate date, LocalTime time) {
        slotRepository.findByCenterIdAndDateAndTimeSlot(centerId, date, time)
            .ifPresent(slot -> {
                slot.setIsAvailable(true);
                slot.setBookedByDriverId(null);
                slot.setCheckupId(null);
                slotRepository.save(slot);
            });
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicalCheckupDTO> getDriverCheckupHistory(Integer driverId) {
        List<TechnicalCheckup> checkups = checkupRepository.findByDriverIdOrderByCreatedDateDesc(driverId);
        return checkups.stream()
                .map(TechnicalCheckupDTO::new)
                .collect(Collectors.toList());
    }

    @Override
    public TechnicalCheckupDTO completeCheckup(Integer checkupId, String result, String certificateNumber) {
        TechnicalCheckup checkup = checkupRepository.findById(checkupId)
                .orElseThrow(() -> new RuntimeException("Technical checkup not found with id: " + checkupId));
        
        checkup.setStatus(TechnicalCheckup.CheckupStatus.COMPLETED);
        checkup.setResult(result);
        checkup.setCertificateNumber(certificateNumber);
        
        // Set expiry date (typically 1 year from completion)
        if ("PASSED".equals(result)) {
            checkup.setExpiryDate(LocalDate.now().plusYears(1));
        }
        
        checkup.setUpdatedDate(LocalDateTime.now());
        
        TechnicalCheckup updatedCheckup = checkupRepository.save(checkup);
        return new TechnicalCheckupDTO(updatedCheckup);
    }
}
