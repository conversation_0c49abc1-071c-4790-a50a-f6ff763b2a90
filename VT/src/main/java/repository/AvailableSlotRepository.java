package repository;

import model.AvailableSlot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface AvailableSlotRepository extends JpaRepository<AvailableSlot, Integer> {
    
    List<AvailableSlot> findByCenterIdAndIsAvailableTrue(Integer centerId);
    
    List<AvailableSlot> findByCenterIdAndDateAndIsAvailableTrue(Integer centerId, LocalDate date);
    
    @Query("SELECT as FROM AvailableSlot as WHERE as.centerId = :centerId AND as.date >= :startDate AND as.date <= :endDate AND as.isAvailable = true ORDER BY as.date, as.timeSlot")
    List<AvailableSlot> findAvailableSlotsByCenterAndDateRange(@Param("centerId") Integer centerId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    List<AvailableSlot> findByBookedByDriverId(Integer driverId);
    
    @Query("SELECT as FROM AvailableSlot as WHERE as.date >= :startDate AND as.isAvailable = true ORDER BY as.centerId, as.date, as.timeSlot")
    List<AvailableSlot> findUpcomingAvailableSlots(@Param("startDate") LocalDate startDate);
    
    @Query("SELECT as FROM AvailableSlot as WHERE as.centerId = :centerId AND as.date = :date AND as.timeSlot = :time")
    Optional<AvailableSlot> findByCenterIdAndDateAndTimeSlot(
            @Param("centerId") Integer centerId,
            @Param("date") LocalDate date,
            @Param("time") LocalTime time
    );
}
