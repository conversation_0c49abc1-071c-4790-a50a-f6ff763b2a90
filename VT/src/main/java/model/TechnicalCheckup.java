package model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "technical_checkups")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalCheckup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "driver_id", nullable = false)
    private Integer driverId;

    @Column(name = "vehicle_id")
    private Integer vehicleId;

    @Column(name = "vehicle_registration", nullable = false)
    private String vehicleRegistration;

    @Column(name = "vehicle_type")
    private String vehicleType;

    @Column(name = "vehicle_model")
    private String vehicleModel;

    @Column(name = "vehicle_year")
    private String vehicleYear;

    @Column(name = "chassis_number")
    private String chassisNumber;

    @Column(name = "vehicle_color")
    private String vehicleColor;

    @Column(name = "vehicle_fuel_type")
    private String vehicleFuelType;

    @Column(name = "center_id")
    private Integer centerId;

    @Column(name = "center_name")
    private String centerName;

    @Column(name = "appointment_date")
    private LocalDate appointmentDate;

    @Column(name = "appointment_time")
    private String appointmentTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private CheckupStatus status = CheckupStatus.PENDING;

    @Column(name = "created_date")
    private LocalDateTime createdDate = LocalDateTime.now();

    @Column(name = "updated_date")
    private LocalDateTime updatedDate = LocalDateTime.now();

    @Column(name = "notes", length = 1000)
    private String notes;

    @Column(name = "blockchain_tx_hash")
    private String blockchainTxHash;

    @Column(name = "result")
    private String result; // PASSED, FAILED, PENDING

    @Column(name = "certificate_number")
    private String certificateNumber;

    @Column(name = "expiry_date")
    private LocalDate expiryDate;

    @Column(name = "inspector_name")
    private String inspectorName;

    @Column(name = "inspection_details", length = 2000)
    private String inspectionDetails;

    @Column(name = "defects_found", length = 1000)
    private String defectsFound;

    @Column(name = "recommendations", length = 1000)
    private String recommendations;

    public enum CheckupStatus {
        PENDING,
        SCHEDULED,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED,
        FAILED,
        PASSED
    }

    // Helper methods
    public boolean isCompleted() {
        return status == CheckupStatus.COMPLETED || 
               status == CheckupStatus.PASSED || 
               status == CheckupStatus.FAILED;
    }

    public boolean canBeModified() {
        return status == CheckupStatus.PENDING || status == CheckupStatus.SCHEDULED;
    }

    public boolean isActive() {
        return status != CheckupStatus.CANCELLED;
    }
}
