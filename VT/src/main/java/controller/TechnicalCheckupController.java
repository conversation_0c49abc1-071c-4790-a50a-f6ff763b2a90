package controller;

import dto.CreateCheckupRequest;
import dto.TechnicalCheckupDTO;
import model.TechnicalCheckup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.TechnicalCheckupService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt/checkups")
public class TechnicalCheckupController {

    @Autowired
    private TechnicalCheckupService checkupService;

    @Autowired
    private service.BlockchainService blockchainService;

    /**
     * Create a new technical checkup request
     */
    @PostMapping
    public ResponseEntity<TechnicalCheckupDTO> createCheckup(
            @RequestBody CreateCheckupRequest request,
            @RequestHeader("X-User-Email") String userEmail) {

        // Extract driver ID from user email or authentication context
        // For now, using a mock driver ID
        Integer driverId = extractDriverIdFromEmail(userEmail);

        TechnicalCheckupDTO checkup = checkupService.createCheckup(request, driverId);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Get driver's checkup history
     */
    @GetMapping("/history")
    public ResponseEntity<List<TechnicalCheckupDTO>> getCheckupHistory(
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<TechnicalCheckupDTO> history = checkupService.getDriverCheckupHistory(driverId);
        return ResponseEntity.ok(history);
    }

    /**
     * Get all checkups for current driver
     */
    @GetMapping
    public ResponseEntity<List<TechnicalCheckupDTO>> getDriverCheckups(
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        List<TechnicalCheckupDTO> checkups = checkupService.getDriverCheckups(driverId);
        return ResponseEntity.ok(checkups);
    }

    /**
     * Get checkup by ID
     */
    @GetMapping("/{checkupId}")
    public ResponseEntity<TechnicalCheckupDTO> getCheckupById(
            @PathVariable Integer checkupId,
            @RequestHeader("X-User-Email") String userEmail) {

        TechnicalCheckupDTO checkup = checkupService.getCheckupById(checkupId);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Schedule appointment for checkup
     */
    @PostMapping("/{checkupId}/schedule")
    public ResponseEntity<TechnicalCheckupDTO> scheduleAppointment(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> scheduleRequest,
            @RequestHeader(value = "X-User-Email", required = false, defaultValue = "<EMAIL>") String userEmail) {

        try {
            System.out.println("📅 VT Service: Scheduling appointment for checkup: " + checkupId);
            System.out.println("📅 VT Service: User email: " + userEmail);
            System.out.println("📅 VT Service: Schedule request: " + scheduleRequest);

            Integer centerId = Integer.parseInt(scheduleRequest.get("centerId"));
            String appointmentDate = scheduleRequest.get("appointmentDate");
            String appointmentTime = scheduleRequest.get("appointmentTime");

            System.out.println("📅 VT Service: Parsed - Center ID: " + centerId + ", Date: " + appointmentDate + ", Time: " + appointmentTime);

            TechnicalCheckupDTO checkup = checkupService.scheduleAppointment(
                checkupId, centerId, appointmentDate, appointmentTime);

            System.out.println("✅ VT Service: Appointment scheduled successfully");
            return ResponseEntity.ok(checkup);
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error scheduling appointment: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Cancel checkup
     */
    @DeleteMapping("/{checkupId}")
    public ResponseEntity<Map<String, String>> cancelCheckup(
            @PathVariable Integer checkupId,
            @RequestHeader("X-User-Email") String userEmail) {

        Integer driverId = extractDriverIdFromEmail(userEmail);
        checkupService.cancelCheckup(checkupId, driverId);

        Map<String, String> response = new HashMap<>();
        response.put("message", "Checkup cancelled successfully");

        return ResponseEntity.ok(response);
    }

    /**
     * Update checkup status (admin function)
     */
    @PatchMapping("/{checkupId}/status")
    public ResponseEntity<TechnicalCheckupDTO> updateCheckupStatus(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> statusRequest) {

        String status = statusRequest.get("status");
        TechnicalCheckup.CheckupStatus checkupStatus = TechnicalCheckup.CheckupStatus.valueOf(status);

        TechnicalCheckupDTO checkup = checkupService.updateCheckupStatus(checkupId, checkupStatus);
        return ResponseEntity.ok(checkup);
    }

    /**
     * Complete checkup with result (admin function)
     */
    @PostMapping("/{checkupId}/complete")
    public ResponseEntity<TechnicalCheckupDTO> completeCheckup(
            @PathVariable Integer checkupId,
            @RequestBody Map<String, String> completionRequest) {

        try {
            System.out.println("🏁 VT Service: Completing checkup " + checkupId);
            System.out.println("🏁 VT Service: Request data: " + completionRequest);

            String result = completionRequest.get("result");
            String certificateNumber = completionRequest.get("certificateNumber");

            System.out.println("🏁 VT Service: Result: " + result + ", Certificate: " + certificateNumber);

            TechnicalCheckupDTO checkup = checkupService.completeCheckup(checkupId, result, certificateNumber);
            System.out.println("✅ VT Service: Checkup completed successfully");
            return ResponseEntity.ok(checkup);
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error completing checkup: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test endpoint to check if checkup exists
     */
    @GetMapping("/{checkupId}/test")
    public ResponseEntity<Map<String, Object>> testCheckup(@PathVariable Integer checkupId) {
        try {
            TechnicalCheckupDTO checkup = checkupService.getCheckupById(checkupId);
            Map<String, Object> response = new HashMap<>();
            response.put("exists", true);
            response.put("checkup", checkup);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("exists", false);
            response.put("error", e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * Test blockchain recording
     */
    @PostMapping("/test-blockchain")
    public ResponseEntity<Map<String, Object>> testBlockchain() {
        try {
            System.out.println("🧪 Testing blockchain recording...");

            // Test blockchain recording with mock data
            String txHash = checkupService.testBlockchainRecording();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("transactionHash", txHash);
            response.put("message", "Blockchain test completed");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("❌ Blockchain test failed: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());

            return ResponseEntity.ok(response);
        }
    }

    /**
     * Get blockchain status
     */
    @GetMapping("/blockchain-status")
    public ResponseEntity<Map<String, Object>> getBlockchainStatus() {
        try {
            Map<String, Object> status = blockchainService.getBlockchainStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("error", e.getMessage());
            response.put("isAvailable", false);
            return ResponseEntity.ok(response);
        }
    }

    // Helper method to extract driver ID from email
    // In a real implementation, this would query the user service
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
