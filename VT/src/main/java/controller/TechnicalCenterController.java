package controller;

import dto.AvailableSlotDTO;
import dto.TechnicalCenterDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import service.TechnicalCenterService;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/vt/centers")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:8080"})
public class TechnicalCenterController {

    @Autowired
    private TechnicalCenterService centerService;

    /**
     * Get all active technical centers
     */
    @GetMapping
    public ResponseEntity<List<TechnicalCenterDTO>> getAllCenters() {
        try {
            System.out.println("🏢 VT Service: Getting all active technical centers");
            List<TechnicalCenterDTO> centers = centerService.getAllActiveCenters();
            System.out.println("✅ VT Service: Found " + centers.size() + " active centers");
            return ResponseEntity.ok(centers);
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error getting technical centers: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get centers by city
     */
    @GetMapping("/city/{city}")
    public ResponseEntity<List<TechnicalCenterDTO>> getCentersByCity(@PathVariable String city) {
        try {
            System.out.println("🏢 VT Service: Getting technical centers for city: " + city);
            List<TechnicalCenterDTO> centers = centerService.getCentersByCity(city);
            System.out.println("✅ VT Service: Found " + centers.size() + " centers in " + city);
            return ResponseEntity.ok(centers);
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error getting centers by city: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get center by ID
     */
    @GetMapping("/{centerId}")
    public ResponseEntity<TechnicalCenterDTO> getCenterById(@PathVariable Integer centerId) {
        try {
            TechnicalCenterDTO center = centerService.getCenterById(centerId);
            return ResponseEntity.ok(center);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get available dates for a center
     */
    @GetMapping("/{centerId}/available-dates")
    public ResponseEntity<List<AvailableSlotDTO>> getAvailableDates(@PathVariable Integer centerId) {
        try {
            System.out.println("📅 VT Service: Getting available dates for center: " + centerId);
            List<AvailableSlotDTO> availableDates = centerService.getAvailableDates(centerId);
            System.out.println("✅ VT Service: Found " + availableDates.size() + " available dates");
            return ResponseEntity.ok(availableDates);
        } catch (RuntimeException e) {
            System.err.println("❌ VT Service: Center not found: " + e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error getting available dates: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get available slots for a center on a specific date
     */
    @GetMapping("/{centerId}/available-slots")
    public ResponseEntity<List<AvailableSlotDTO>> getAvailableSlots(
            @PathVariable Integer centerId,
            @RequestParam String date) {

        try {
            LocalDate localDate = LocalDate.parse(date);
            List<AvailableSlotDTO> availableSlots = centerService.getAvailableSlots(centerId, localDate);
            return ResponseEntity.ok(availableSlots);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Book a slot
     */
    @PostMapping("/slots/{slotId}/book")
    public ResponseEntity<AvailableSlotDTO> bookSlot(
            @PathVariable Integer slotId,
            @RequestBody Map<String, Object> bookingRequest,
            @RequestHeader(value = "X-User-Email", required = false, defaultValue = "<EMAIL>") String userEmail) {

        try {
            System.out.println("📝 VT Service: Booking slot " + slotId + " for user: " + userEmail);

            Integer driverId = extractDriverIdFromEmail(userEmail);
            Integer checkupId = (Integer) bookingRequest.get("checkupId");

            AvailableSlotDTO bookedSlot = centerService.bookSlot(slotId, driverId, checkupId);
            System.out.println("✅ VT Service: Successfully booked slot");
            return ResponseEntity.ok(bookedSlot);
        } catch (RuntimeException e) {
            System.err.println("❌ VT Service: Booking failed: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            System.err.println("❌ VT Service: Error booking slot: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Generate available slots for a center (admin function)
     */
    @PostMapping("/{centerId}/generate-slots")
    public ResponseEntity<Map<String, String>> generateSlots(
            @PathVariable Integer centerId,
            @RequestBody Map<String, String> request) {

        try {
            LocalDate startDate = LocalDate.parse(request.get("startDate"));
            LocalDate endDate = LocalDate.parse(request.get("endDate"));

            centerService.generateAvailableSlots(centerId, startDate, endDate);

            Map<String, String> response = new HashMap<>();
            response.put("message", "Available slots generated successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", "Failed to generate slots: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Test endpoint for technical centers
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Technical Centers endpoint is working!");
        response.put("timestamp", java.time.LocalDateTime.now());
        response.put("service", "VT Service - Technical Centers");
        return ResponseEntity.ok(response);
    }

    /**
     * Handle OPTIONS requests for CORS preflight
     */
    @RequestMapping(method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return ResponseEntity.ok().build();
    }

    // Helper method to extract driver ID from email
    private Integer extractDriverIdFromEmail(String email) {
        // Mock implementation - return a fixed driver ID for testing
        return 1;
    }
}
