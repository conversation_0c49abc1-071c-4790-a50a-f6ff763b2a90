package Contracts;

import io.reactivex.Flowable;
import io.reactivex.functions.Function;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.RemoteCall;
import org.web3j.protocol.core.RemoteFunctionCall;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.BaseEventResponse;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.tuples.generated.Tuple7;
import org.web3j.tuples.generated.Tuple8;
import org.web3j.tx.Contract;
import org.web3j.tx.TransactionManager;
import org.web3j.tx.gas.ContractGasProvider;

/**
 * <p>Auto generated code.
 * <p><strong>Do not modify!</strong>
 * <p>Please use the <a href="https://docs.web3j.io/command_line.html">web3j command line tools</a>,
 * or the org.web3j.codegen.SolidityFunctionWrapperGenerator in the 
 * <a href="https://github.com/web3j/web3j/tree/master/codegen">codegen module</a> to update.
 *
 * <p>Generated with web3j version 4.9.4.
 */
@SuppressWarnings("rawtypes")
public class CheckupHistory extends Contract {
    public static final String BINARY = "6080604052348015600e575f5ffd5b50600380546001600160a01b031916331790555f600255611000806100325f395ff3fe608060405234801561000f575f5ffd5b506004361061007a575f3560e01c80638dd04f6f116100585780638dd04f6f146100d8578063c518543c146100eb578063ec15d77b1461010b578063f4a3fd1c14610132575f5ffd5b8063172e4d3d1461007e57806327459d1c146100a45780638da5cb5b146100ad575b5f5ffd5b61009161008c366004610b2f565b610158565b6040519081526020015b60405180910390f35b61009160025481565b6003546100c0906001600160a01b031681565b6040516001600160a01b03909116815260200161009b565b6100916100e6366004610c1a565b610357565b6100fe6100f9366004610c5c565b610393565b60405161009b9190610c96565b61011e610119366004610cd8565b610401565b60405161009b989796959493929190610d1d565b610145610140366004610cd8565b6106e2565b60405161009b9796959493929190610da3565b6003545f906001600160a01b031633146101c35760405162461bcd60e51b815260206004820152602160248201527f4f6e6c79206f776e65722063616e2063616c6c20746869732066756e6374696f6044820152603760f91b60648201526084015b60405180910390fd5b60028054905f6101d283610e1d565b91905055506040518061010001604052806002548152602001888152602001878152602001868152602001858152602001848152602001838152602001600115158152505f5f60025481526020019081526020015f205f820151815f015560208201518160010190816102459190610ec5565b506040820151600282019061025a9082610ec5565b5060608201516003820155608082015160048201906102799082610ec5565b5060a0820151600582019061028e9082610ec5565b5060c082015160068201906102a39082610ec5565b5060e091909101516007909101805460ff19169115159190911790556040516001906102d0908990610f80565b908152604051908190036020908101822060025481546001810183555f9283529290912090910155610303908890610f80565b60405180910390206002547f38b6a2b6643b656126568cf2d33abdb949f9e74e339b12cd2e0ee03cfb263b6788888860405161034193929190610f96565b60405180910390a3506002549695505050505050565b81516020818401810180516001825292820191850191909120919052805482908110610381575f80fd5b905f5260205f20015f91509150505481565b60606001826040516103a59190610f80565b90815260408051918290036020908101832080548083028501830190935282845291908301828280156103f557602002820191905f5260205f20905b8154815260200190600101908083116103e1575b50505050509050919050565b5f602081905290815260409020805460018201805491929161042290610e41565b80601f016020809104026020016040519081016040528092919081815260200182805461044e90610e41565b80156104995780601f1061047057610100808354040283529160200191610499565b820191905f5260205f20905b81548152906001019060200180831161047c57829003601f168201915b5050505050908060020180546104ae90610e41565b80601f01602080910402602001604051908101604052809291908181526020018280546104da90610e41565b80156105255780601f106104fc57610100808354040283529160200191610525565b820191905f5260205f20905b81548152906001019060200180831161050857829003601f168201915b50505050509080600301549080600401805461054090610e41565b80601f016020809104026020016040519081016040528092919081815260200182805461056c90610e41565b80156105b75780601f1061058e576101008083540402835291602001916105b7565b820191905f5260205f20905b81548152906001019060200180831161059a57829003601f168201915b5050505050908060050180546105cc90610e41565b80601f01602080910402602001604051908101604052809291908181526020018280546105f890610e41565b80156106435780601f1061061a57610100808354040283529160200191610643565b820191905f5260205f20905b81548152906001019060200180831161062657829003601f168201915b50505050509080600601805461065890610e41565b80601f016020809104026020016040519081016040528092919081815260200182805461068490610e41565b80156106cf5780601f106106a6576101008083540402835291602001916106cf565b820191905f5260205f20905b8154815290600101906020018083116106b257829003601f168201915b5050506007909301549192505060ff1688565b5f818152602081905260408120600701546060908190839082908190819060ff166107485760405162461bcd60e51b815260206004820152601660248201527510da1958dadd5c08191bd95cc81b9bdd08195e1a5cdd60521b60448201526064016101ba565b5f5f5f8a81526020019081526020015f20604051806101000160405290815f820154815260200160018201805461077e90610e41565b80601f01602080910402602001604051908101604052809291908181526020018280546107aa90610e41565b80156107f55780601f106107cc576101008083540402835291602001916107f5565b820191905f5260205f20905b8154815290600101906020018083116107d857829003601f168201915b5050505050815260200160028201805461080e90610e41565b80601f016020809104026020016040519081016040528092919081815260200182805461083a90610e41565b80156108855780601f1061085c57610100808354040283529160200191610885565b820191905f5260205f20905b81548152906001019060200180831161086857829003601f168201915b50505050508152602001600382015481526020016004820180546108a890610e41565b80601f01602080910402602001604051908101604052809291908181526020018280546108d490610e41565b801561091f5780601f106108f65761010080835404028352916020019161091f565b820191905f5260205f20905b81548152906001019060200180831161090257829003601f168201915b5050505050815260200160058201805461093890610e41565b80601f016020809104026020016040519081016040528092919081815260200182805461096490610e41565b80156109af5780601f10610986576101008083540402835291602001916109af565b820191905f5260205f20905b81548152906001019060200180831161099257829003601f168201915b505050505081526020016006820180546109c890610e41565b80601f01602080910402602001604051908101604052809291908181526020018280546109f490610e41565b8015610a3f5780601f10610a1657610100808354040283529160200191610a3f565b820191905f5260205f20905b815481529060010190602001808311610a2257829003601f168201915b50505091835250506007919091015460ff16151560209182015281519082015160408301516060840151608085015160a086015160c090960151949f939e50919c509a509850919650945092505050565b634e487b7160e01b5f52604160045260245ffd5b5f82601f830112610ab3575f5ffd5b813567ffffffffffffffff811115610acd57610acd610a90565b604051601f8201601f19908116603f0116810167ffffffffffffffff81118282101715610afc57610afc610a90565b604052818152838201602001851015610b13575f5ffd5b816020850160208301375f918101602001919091529392505050565b5f5f5f5f5f5f60c08789031215610b44575f5ffd5b863567ffffffffffffffff811115610b5a575f5ffd5b610b6689828a01610aa4565b965050602087013567ffffffffffffffff811115610b82575f5ffd5b610b8e89828a01610aa4565b95505060408701359350606087013567ffffffffffffffff811115610bb1575f5ffd5b610bbd89828a01610aa4565b935050608087013567ffffffffffffffff811115610bd9575f5ffd5b610be589828a01610aa4565b92505060a087013567ffffffffffffffff811115610c01575f5ffd5b610c0d89828a01610aa4565b9150509295509295509295565b5f5f60408385031215610c2b575f5ffd5b823567ffffffffffffffff811115610c41575f5ffd5b610c4d85828601610aa4565b95602094909401359450505050565b5f60208284031215610c6c575f5ffd5b813567ffffffffffffffff811115610c82575f5ffd5b610c8e84828501610aa4565b949350505050565b602080825282518282018190525f918401906040840190835b81811015610ccd578351835260209384019390920191600101610caf565b509095945050505050565b5f60208284031215610ce8575f5ffd5b5035919050565b5f81518084528060208401602086015e5f602082860101526020601f19601f83011685010191505092915050565b88815261010060208201525f610d3761010083018a610cef565b8281036040840152610d49818a610cef565b90508760608401528281036080840152610d638188610cef565b905082810360a0840152610d778187610cef565b905082810360c0840152610d8b8186610cef565b91505082151560e08301529998505050505050505050565b87815260e060208201525f610dbb60e0830189610cef565b8281036040840152610dcd8189610cef565b90508660608401528281036080840152610de78187610cef565b905082810360a0840152610dfb8186610cef565b905082810360c0840152610e0f8185610cef565b9a9950505050505050505050565b5f60018201610e3a57634e487b7160e01b5f52601160045260245ffd5b5060010190565b600181811c90821680610e5557607f821691505b602082108103610e7357634e487b7160e01b5f52602260045260245ffd5b50919050565b601f821115610ec057805f5260205f20601f840160051c81016020851015610e9e5750805b601f840160051c820191505b81811015610ebd575f8155600101610eaa565b50505b505050565b815167ffffffffffffffff811115610edf57610edf610a90565b610ef381610eed8454610e41565b84610e79565b6020601f821160018114610f25575f8315610f0e5750848201515b5f19600385901b1c1916600184901b178455610ebd565b5f84815260208120601f198516915b82811015610f545787850151825560209485019460019092019101610f34565b5084821015610f7157868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b5f82518060208501845e5f920191825250919050565b606081525f610fa86060830186610cef565b8460208401528281036040840152610fc08185610cef565b969550505050505056fea26469706673582212204c7b7371dbfefd080d00ede324beb5ad89e33092f9059a73bf53f6b5d082ad4764736f6c634300081d0033";

    public static final String FUNC_CHECKUPCOUNT = "checkupCount";

    public static final String FUNC_CHECKUPS = "checkups";

    public static final String FUNC_CHECKUPSBYVEHICLE = "checkupsByVehicle";

    public static final String FUNC_GETCHECKUP = "getCheckup";

    public static final String FUNC_GETCHECKUPSBYVEHICLE = "getCheckupsByVehicle";

    public static final String FUNC_OWNER = "owner";

    public static final String FUNC_RECORDCHECKUP = "recordCheckup";

    public static final Event CHECKUPRECORDED_EVENT = new Event("CheckupRecorded", 
            Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>(true) {}, new TypeReference<Utf8String>(true) {}, new TypeReference<Utf8String>() {}, new TypeReference<Uint256>() {}, new TypeReference<Utf8String>() {}));
    ;

    @Deprecated
    protected CheckupHistory(String contractAddress, Web3j web3j, Credentials credentials, BigInteger gasPrice, BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    protected CheckupHistory(String contractAddress, Web3j web3j, Credentials credentials, ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, credentials, contractGasProvider);
    }

    @Deprecated
    protected CheckupHistory(String contractAddress, Web3j web3j, TransactionManager transactionManager, BigInteger gasPrice, BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    protected CheckupHistory(String contractAddress, Web3j web3j, TransactionManager transactionManager, ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, transactionManager, contractGasProvider);
    }

    public static List<CheckupRecordedEventResponse> getCheckupRecordedEvents(TransactionReceipt transactionReceipt) {
        List<Contract.EventValuesWithLog> valueList = staticExtractEventParametersWithLog(CHECKUPRECORDED_EVENT, transactionReceipt);
        ArrayList<CheckupRecordedEventResponse> responses = new ArrayList<CheckupRecordedEventResponse>(valueList.size());
        for (Contract.EventValuesWithLog eventValues : valueList) {
            CheckupRecordedEventResponse typedResponse = new CheckupRecordedEventResponse();
            typedResponse.log = eventValues.getLog();
            typedResponse.id = (BigInteger) eventValues.getIndexedValues().get(0).getValue();
            typedResponse.vehicleRegistration = (byte[]) eventValues.getIndexedValues().get(1).getValue();
            typedResponse.centerName = (String) eventValues.getNonIndexedValues().get(0).getValue();
            typedResponse.checkupDate = (BigInteger) eventValues.getNonIndexedValues().get(1).getValue();
            typedResponse.status = (String) eventValues.getNonIndexedValues().get(2).getValue();
            responses.add(typedResponse);
        }
        return responses;
    }

    public Flowable<CheckupRecordedEventResponse> checkupRecordedEventFlowable(EthFilter filter) {
        return web3j.ethLogFlowable(filter).map(new Function<Log, CheckupRecordedEventResponse>() {
            @Override
            public CheckupRecordedEventResponse apply(Log log) {
                Contract.EventValuesWithLog eventValues = extractEventParametersWithLog(CHECKUPRECORDED_EVENT, log);
                CheckupRecordedEventResponse typedResponse = new CheckupRecordedEventResponse();
                typedResponse.log = log;
                typedResponse.id = (BigInteger) eventValues.getIndexedValues().get(0).getValue();
                typedResponse.vehicleRegistration = (byte[]) eventValues.getIndexedValues().get(1).getValue();
                typedResponse.centerName = (String) eventValues.getNonIndexedValues().get(0).getValue();
                typedResponse.checkupDate = (BigInteger) eventValues.getNonIndexedValues().get(1).getValue();
                typedResponse.status = (String) eventValues.getNonIndexedValues().get(2).getValue();
                return typedResponse;
            }
        });
    }

    public Flowable<CheckupRecordedEventResponse> checkupRecordedEventFlowable(DefaultBlockParameter startBlock, DefaultBlockParameter endBlock) {
        EthFilter filter = new EthFilter(startBlock, endBlock, getContractAddress());
        filter.addSingleTopic(EventEncoder.encode(CHECKUPRECORDED_EVENT));
        return checkupRecordedEventFlowable(filter);
    }

    public RemoteFunctionCall<BigInteger> checkupCount() {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_CHECKUPCOUNT, 
                Arrays.<Type>asList(), 
                Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}));
        return executeRemoteCallSingleValueReturn(function, BigInteger.class);
    }

    public RemoteFunctionCall<Tuple8<BigInteger, String, String, BigInteger, String, String, String, Boolean>> checkups(BigInteger param0) {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_CHECKUPS, 
                Arrays.<Type>asList(new org.web3j.abi.datatypes.generated.Uint256(param0)), 
                Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Uint256>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Bool>() {}));
        return new RemoteFunctionCall<Tuple8<BigInteger, String, String, BigInteger, String, String, String, Boolean>>(function,
                new Callable<Tuple8<BigInteger, String, String, BigInteger, String, String, String, Boolean>>() {
                    @Override
                    public Tuple8<BigInteger, String, String, BigInteger, String, String, String, Boolean> call() throws Exception {
                        List<Type> results = executeCallMultipleValueReturn(function);
                        return new Tuple8<BigInteger, String, String, BigInteger, String, String, String, Boolean>(
                                (BigInteger) results.get(0).getValue(), 
                                (String) results.get(1).getValue(), 
                                (String) results.get(2).getValue(), 
                                (BigInteger) results.get(3).getValue(), 
                                (String) results.get(4).getValue(), 
                                (String) results.get(5).getValue(), 
                                (String) results.get(6).getValue(), 
                                (Boolean) results.get(7).getValue());
                    }
                });
    }

    public RemoteFunctionCall<BigInteger> checkupsByVehicle(String param0, BigInteger param1) {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_CHECKUPSBYVEHICLE, 
                Arrays.<Type>asList(new org.web3j.abi.datatypes.Utf8String(param0), 
                new org.web3j.abi.datatypes.generated.Uint256(param1)), 
                Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}));
        return executeRemoteCallSingleValueReturn(function, BigInteger.class);
    }

    public RemoteFunctionCall<Tuple7<BigInteger, String, String, BigInteger, String, String, String>> getCheckup(BigInteger _id) {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_GETCHECKUP, 
                Arrays.<Type>asList(new org.web3j.abi.datatypes.generated.Uint256(_id)), 
                Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Uint256>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}, new TypeReference<Utf8String>() {}));
        return new RemoteFunctionCall<Tuple7<BigInteger, String, String, BigInteger, String, String, String>>(function,
                new Callable<Tuple7<BigInteger, String, String, BigInteger, String, String, String>>() {
                    @Override
                    public Tuple7<BigInteger, String, String, BigInteger, String, String, String> call() throws Exception {
                        List<Type> results = executeCallMultipleValueReturn(function);
                        return new Tuple7<BigInteger, String, String, BigInteger, String, String, String>(
                                (BigInteger) results.get(0).getValue(), 
                                (String) results.get(1).getValue(), 
                                (String) results.get(2).getValue(), 
                                (BigInteger) results.get(3).getValue(), 
                                (String) results.get(4).getValue(), 
                                (String) results.get(5).getValue(), 
                                (String) results.get(6).getValue());
                    }
                });
    }

    public RemoteFunctionCall<List> getCheckupsByVehicle(String _vehicleRegistration) {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_GETCHECKUPSBYVEHICLE, 
                Arrays.<Type>asList(new org.web3j.abi.datatypes.Utf8String(_vehicleRegistration)), 
                Arrays.<TypeReference<?>>asList(new TypeReference<DynamicArray<Uint256>>() {}));
        return new RemoteFunctionCall<List>(function,
                new Callable<List>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public List call() throws Exception {
                        List<Type> result = (List<Type>) executeCallSingleValueReturn(function, List.class);
                        return convertToNative(result);
                    }
                });
    }

    public RemoteFunctionCall<String> owner() {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(FUNC_OWNER, 
                Arrays.<Type>asList(), 
                Arrays.<TypeReference<?>>asList(new TypeReference<Address>() {}));
        return executeRemoteCallSingleValueReturn(function, String.class);
    }

    public RemoteFunctionCall<TransactionReceipt> recordCheckup(String _vehicleRegistration, String _centerName, BigInteger _checkupDate, String _status, String _result, String _certificateNumber) {
        final org.web3j.abi.datatypes.Function function = new org.web3j.abi.datatypes.Function(
                FUNC_RECORDCHECKUP, 
                Arrays.<Type>asList(new org.web3j.abi.datatypes.Utf8String(_vehicleRegistration), 
                new org.web3j.abi.datatypes.Utf8String(_centerName), 
                new org.web3j.abi.datatypes.generated.Uint256(_checkupDate), 
                new org.web3j.abi.datatypes.Utf8String(_status), 
                new org.web3j.abi.datatypes.Utf8String(_result), 
                new org.web3j.abi.datatypes.Utf8String(_certificateNumber)), 
                Collections.<TypeReference<?>>emptyList());
        return executeRemoteCallTransaction(function);
    }

    @Deprecated
    public static CheckupHistory load(String contractAddress, Web3j web3j, Credentials credentials, BigInteger gasPrice, BigInteger gasLimit) {
        return new CheckupHistory(contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    @Deprecated
    public static CheckupHistory load(String contractAddress, Web3j web3j, TransactionManager transactionManager, BigInteger gasPrice, BigInteger gasLimit) {
        return new CheckupHistory(contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    public static CheckupHistory load(String contractAddress, Web3j web3j, Credentials credentials, ContractGasProvider contractGasProvider) {
        return new CheckupHistory(contractAddress, web3j, credentials, contractGasProvider);
    }

    public static CheckupHistory load(String contractAddress, Web3j web3j, TransactionManager transactionManager, ContractGasProvider contractGasProvider) {
        return new CheckupHistory(contractAddress, web3j, transactionManager, contractGasProvider);
    }

    public static RemoteCall<CheckupHistory> deploy(Web3j web3j, Credentials credentials, ContractGasProvider contractGasProvider) {
        return deployRemoteCall(CheckupHistory.class, web3j, credentials, contractGasProvider, BINARY, "");
    }

    public static RemoteCall<CheckupHistory> deploy(Web3j web3j, TransactionManager transactionManager, ContractGasProvider contractGasProvider) {
        return deployRemoteCall(CheckupHistory.class, web3j, transactionManager, contractGasProvider, BINARY, "");
    }

    @Deprecated
    public static RemoteCall<CheckupHistory> deploy(Web3j web3j, Credentials credentials, BigInteger gasPrice, BigInteger gasLimit) {
        return deployRemoteCall(CheckupHistory.class, web3j, credentials, gasPrice, gasLimit, BINARY, "");
    }

    @Deprecated
    public static RemoteCall<CheckupHistory> deploy(Web3j web3j, TransactionManager transactionManager, BigInteger gasPrice, BigInteger gasLimit) {
        return deployRemoteCall(CheckupHistory.class, web3j, transactionManager, gasPrice, gasLimit, BINARY, "");
    }

    public static class CheckupRecordedEventResponse extends BaseEventResponse {
        public BigInteger id;

        public byte[] vehicleRegistration;

        public String centerName;

        public BigInteger checkupDate;

        public String status;
    }
}
